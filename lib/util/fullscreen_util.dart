import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// Utility class for handling fullscreen mode across different Android versions
class FullscreenUtil {
  static int? _androidSdkVersion;
  
  /// Get Android SDK version
  static Future<int> _getAndroidSdkVersion() async {
    if (_androidSdkVersion != null) return _androidSdkVersion!;
    
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      _androidSdkVersion = androidInfo.version.sdkInt;
      return _androidSdkVersion!;
    }
    return 0;
  }
  
  /// Hide system UI with Android version-specific optimizations
  static Future<void> hideSystemUI() async {
    if (!Platform.isAndroid) {
      // For non-Android platforms, use standard immersive mode
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
      return;
    }
    
    final sdkVersion = await _getAndroidSdkVersion();
    
    if (sdkVersion >= 30) {
      // Android 11+ (API 30+) - Use the new immersive sticky mode
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );
    } else if (sdkVersion >= 28) {
      // Android 9-10 (API 28-29) - Handle gesture navigation
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );
    } else {
      // Android 8.1 and below (API 27 and below) - Traditional navigation
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );
    }
    
    // Set system UI overlay style for all Android versions
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarContrastEnforced: false,
      ),
    );
  }
  
  /// Show system UI with Android version-specific optimizations
  static Future<void> showSystemUI() async {
    if (!Platform.isAndroid) {
      // For non-Android platforms, use standard edge-to-edge mode
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      return;
    }
    
    final sdkVersion = await _getAndroidSdkVersion();
    
    // Restore system UI for all Android versions
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: SystemUiOverlay.values,
    );
    
    // Set appropriate overlay style based on Android version
    if (sdkVersion >= 30) {
      // Android 11+ - Modern system bars
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
          systemNavigationBarDividerColor: Colors.transparent,
          systemNavigationBarContrastEnforced: false,
        ),
      );
    } else if (sdkVersion >= 28) {
      // Android 9-10 - Handle gesture navigation
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.black.withValues(alpha: 0.3),
          systemNavigationBarIconBrightness: Brightness.light,
          systemNavigationBarDividerColor: Colors.transparent,
        ),
      );
    } else {
      // Android 8.1 and below - Traditional navigation
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.black.withValues(alpha: 0.5),
          systemNavigationBarIconBrightness: Brightness.light,
          systemNavigationBarDividerColor: Colors.transparent,
        ),
      );
    }
  }
  
  /// Check if the device supports modern fullscreen features
  static Future<bool> supportsModernFullscreen() async {
    if (!Platform.isAndroid) return true;
    
    final sdkVersion = await _getAndroidSdkVersion();
    return sdkVersion >= 28; // Android 9+
  }
  
  /// Get safe area padding for fullscreen mode
  static EdgeInsets getSafeAreaPadding(BuildContext context, bool isFullscreen) {
    if (!isFullscreen) {
      return MediaQuery.of(context).padding;
    }
    
    // In fullscreen mode, we want to ignore system UI padding
    return EdgeInsets.zero;
  }
}
